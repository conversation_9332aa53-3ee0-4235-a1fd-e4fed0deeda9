# CoreHub Web 生产环境部署脚本
# 使用方法: .\deploy-production.ps1

param(
    [string]$Environment = "Production",
    [string]$PublishPath = ".\publish\production",
    [switch]$ForceHttps = $true,
    [string]$AllowedHosts = "api.saintyeartex.com,*.saintyeartex.com",
    [string]$CorsOrigins = "https://api.saintyeartex.com,https://app.saintyeartex.com"
)

Write-Host "=== CoreHub Web 生产环境部署 ===" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "发布路径: $PublishPath" -ForegroundColor Yellow
Write-Host "强制HTTPS: $ForceHttps" -ForegroundColor Yellow

# 设置环境变量
$env:ASPNETCORE_ENVIRONMENT = $Environment
$env:DEPLOYMENT_ENVIRONMENT = "Production"
$env:FORCE_HTTPS = $ForceHttps.ToString().ToLower()
$env:ALLOWED_HOSTS = $AllowedHosts
$env:CORS_ORIGINS = $CorsOrigins

Write-Host "设置的环境变量:" -ForegroundColor Cyan
Write-Host "  ASPNETCORE_ENVIRONMENT = $env:ASPNETCORE_ENVIRONMENT"
Write-Host "  DEPLOYMENT_ENVIRONMENT = $env:DEPLOYMENT_ENVIRONMENT"
Write-Host "  FORCE_HTTPS = $env:FORCE_HTTPS"
Write-Host "  ALLOWED_HOSTS = $env:ALLOWED_HOSTS"
Write-Host "  CORS_ORIGINS = $env:CORS_ORIGINS"

# 清理之前的发布
if (Test-Path $PublishPath) {
    Write-Host "清理之前的发布文件..." -ForegroundColor Yellow
    Remove-Item $PublishPath -Recurse -Force
}

# 发布应用
Write-Host "开始发布应用..." -ForegroundColor Yellow
dotnet publish CoreHub.Web.csproj -c Release -o $PublishPath --self-contained false

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 应用发布成功!" -ForegroundColor Green
    
    # 创建启动脚本
    $startScript = @"
#!/bin/bash
# CoreHub Web 生产环境启动脚本

export ASPNETCORE_ENVIRONMENT=$Environment
export DEPLOYMENT_ENVIRONMENT=Production
export FORCE_HTTPS=$($ForceHttps.ToString().ToLower())
export ALLOWED_HOSTS="$AllowedHosts"
export CORS_ORIGINS="$CorsOrigins"

echo "=== CoreHub Web 启动 ==="
echo "环境: `$ASPNETCORE_ENVIRONMENT"
echo "部署环境: `$DEPLOYMENT_ENVIRONMENT"
echo "强制HTTPS: `$FORCE_HTTPS"
echo "允许的主机: `$ALLOWED_HOSTS"
echo "CORS源: `$CORS_ORIGINS"

# 启动应用
dotnet CoreHub.Web.dll --urls "http://0.0.0.0:8080;https://0.0.0.0:8081"
"@

    $startScript | Out-File -FilePath "$PublishPath\start-production.sh" -Encoding UTF8
    
    # 创建Windows启动脚本
    $startBat = @"
@echo off
REM CoreHub Web 生产环境启动脚本

set ASPNETCORE_ENVIRONMENT=$Environment
set DEPLOYMENT_ENVIRONMENT=Production
set FORCE_HTTPS=$($ForceHttps.ToString().ToLower())
set ALLOWED_HOSTS=$AllowedHosts
set CORS_ORIGINS=$CorsOrigins

echo === CoreHub Web 启动 ===
echo 环境: %ASPNETCORE_ENVIRONMENT%
echo 部署环境: %DEPLOYMENT_ENVIRONMENT%
echo 强制HTTPS: %FORCE_HTTPS%
echo 允许的主机: %ALLOWED_HOSTS%
echo CORS源: %CORS_ORIGINS%

REM 启动应用
dotnet CoreHub.Web.dll --urls "http://0.0.0.0:8080;https://0.0.0.0:8081"
"@

    $startBat | Out-File -FilePath "$PublishPath\start-production.bat" -Encoding UTF8
    
    Write-Host "✅ 启动脚本已创建:" -ForegroundColor Green
    Write-Host "  Linux/Mac: start-production.sh" -ForegroundColor Cyan
    Write-Host "  Windows: start-production.bat" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "=== 部署完成 ===" -ForegroundColor Green
    Write-Host "发布文件位置: $PublishPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "下一步操作:" -ForegroundColor Cyan
    Write-Host "1. 将发布文件复制到生产服务器" -ForegroundColor White
    Write-Host "2. 确保SSL证书文件在正确位置" -ForegroundColor White
    Write-Host "3. 运行启动脚本: ./start-production.sh 或 start-production.bat" -ForegroundColor White
    Write-Host "4. 验证HTTPS访问: https://api.saintyeartex.com:8081" -ForegroundColor White
    
} else {
    Write-Host "❌ 应用发布失败!" -ForegroundColor Red
    exit 1
}
