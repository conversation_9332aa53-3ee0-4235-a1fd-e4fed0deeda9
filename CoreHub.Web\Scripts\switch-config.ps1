# CoreHub Web 配置文件切换脚本
# 使用方法: .\switch-config.ps1 -Environment Production

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Intranet", "Production")]
    [string]$Environment
)

Write-Host "=== CoreHub Web 配置切换 ===" -ForegroundColor Green
Write-Host "目标环境: $Environment" -ForegroundColor Yellow

# 配置文件路径
$baseConfigPath = "appsettings.json"
$envConfigPath = "appsettings.$Environment.json"
$backupPath = "appsettings.backup.json"

# 检查环境配置文件是否存在
if (-not (Test-Path $envConfigPath)) {
    Write-Host "❌ 环境配置文件不存在: $envConfigPath" -ForegroundColor Red
    exit 1
}

# 备份当前配置
if (Test-Path $baseConfigPath) {
    Write-Host "备份当前配置..." -ForegroundColor Yellow
    Copy-Item $baseConfigPath $backupPath -Force
}

# 读取环境配置
Write-Host "读取环境配置: $envConfigPath" -ForegroundColor Yellow
$envConfig = Get-Content $envConfigPath -Raw | ConvertFrom-Json

# 读取基础配置
$baseConfig = Get-Content $baseConfigPath -Raw | ConvertFrom-Json

# 合并HTTPS策略配置
if ($envConfig.HttpsPolicy) {
    Write-Host "更新HTTPS策略配置..." -ForegroundColor Yellow
    $baseConfig | Add-Member -Type NoteProperty -Name "HttpsPolicy" -Value $envConfig.HttpsPolicy -Force
}

# 更新UpdateService配置
if ($envConfig.UpdateService) {
    Write-Host "更新UpdateService配置..." -ForegroundColor Yellow
    $baseConfig.UpdateService = $envConfig.UpdateService
}

# 更新AllowedHosts
if ($envConfig.AllowedHosts) {
    Write-Host "更新AllowedHosts配置..." -ForegroundColor Yellow
    $baseConfig.AllowedHosts = $envConfig.AllowedHosts
}

# 保存更新后的配置
Write-Host "保存配置文件..." -ForegroundColor Yellow
$baseConfig | ConvertTo-Json -Depth 10 | Set-Content $baseConfigPath -Encoding UTF8

Write-Host "✅ 配置切换完成!" -ForegroundColor Green

# 显示当前HTTPS策略
if ($baseConfig.HttpsPolicy) {
    Write-Host ""
    Write-Host "=== 当前HTTPS策略 ===" -ForegroundColor Cyan
    Write-Host "环境: $($baseConfig.HttpsPolicy.Environment)" -ForegroundColor White
    Write-Host "描述: $($baseConfig.HttpsPolicy.Description)" -ForegroundColor White
    Write-Host "HTTPS重定向: $($baseConfig.HttpsPolicy.UseHttpsRedirection)" -ForegroundColor White
    Write-Host "要求HTTPS: $($baseConfig.HttpsPolicy.RequireHttps)" -ForegroundColor White
    Write-Host "启用HSTS: $($baseConfig.HttpsPolicy.UseHsts)" -ForegroundColor White
}

# 显示UpdateService配置
if ($baseConfig.UpdateService) {
    Write-Host ""
    Write-Host "=== UpdateService配置 ===" -ForegroundColor Cyan
    Write-Host "BaseUrl: $($baseConfig.UpdateService.BaseUrl)" -ForegroundColor White
}

Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
switch ($Environment) {
    "Development" {
        Write-Host "1. 运行: dotnet run --project CoreHub.Web" -ForegroundColor White
        Write-Host "2. 访问: http://localhost:8080" -ForegroundColor White
        Write-Host "3. MAUI配置: http://localhost:8080" -ForegroundColor White
    }
    "Intranet" {
        Write-Host "1. 运行: dotnet run --project CoreHub.Web --urls 'http://0.0.0.0:8080;https://0.0.0.0:8081'" -ForegroundColor White
        Write-Host "2. 访问: http://************:8080" -ForegroundColor White
        Write-Host "3. MAUI配置: http://************:8080" -ForegroundColor White
    }
    "Production" {
        Write-Host "1. 部署到生产服务器" -ForegroundColor White
        Write-Host "2. 访问: https://api.saintyeartex.com:8081" -ForegroundColor White
        Write-Host "3. MAUI配置: https://api.saintyeartex.com:8081" -ForegroundColor White
        Write-Host "4. 确保SSL证书有效" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "恢复配置: Copy-Item $backupPath $baseConfigPath -Force" -ForegroundColor Gray
