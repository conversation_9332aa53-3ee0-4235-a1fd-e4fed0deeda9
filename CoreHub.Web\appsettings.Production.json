{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "api.saintyeartex.com;*.saintyeartex.com", "Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:8080"}, "Https": {"Url": "https://0.0.0.0:8081", "Certificate": {"Path": "Certificates/api_saintyeartex_com.pfx", "Password": "HO4LokI5cBvEuKYT"}, "Protocols": "Http1AndHttp2", "SslProtocols": ["Tls12", "Tls13"]}}}, "CertificateMonitoring": {"Enabled": true, "CheckIntervalHours": 24, "WarningDays": 30, "CriticalDays": 7, "NotificationEmail": "<EMAIL>"}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=CoreHub_Prod;User Id=sa;Password=YourProductionPassword;TrustServerCertificate=True;", "LoggingConnection": "Server=localhost;Database=CoreHub_Logs;User Id=sa;Password=YourProductionPassword;TrustServerCertificate=True;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/corehub-web-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Security": {"RequireHttps": true, "UseHsts": true, "HstsMaxAge": 365, "IncludeSubdomains": true}, "Cors": {"AllowedOrigins": ["https://api.saintyeartex.com", "https://app.saintyeartex.com", "https://admin.saintyeartex.com"], "AllowCredentials": true}}