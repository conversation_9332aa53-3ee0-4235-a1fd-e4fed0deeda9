@using CoreHub.Configuration
@using MudBlazor
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudText Typo="Typo.h6" Class="mb-4">网络环境配置</MudText>
            
            <MudGrid>
                <MudItem xs="12">
                    <MudSelect T="string" 
                              Label="选择环境" 
                              @bind-Value="SelectedEnvironment" 
                              Variant="Variant.Outlined"
                              AnchorOrigin="Origin.BottomCenter">
                        @foreach (var env in AvailableEnvironments)
                        {
                            <MudSelectItem Value="@env">
                                @GetEnvironmentDisplayName(env)
                            </MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>

                @if (CurrentConfig != null)
                {
                    <MudItem xs="12">
                        <MudCard Elevation="2" Class="pa-4 mt-4">
                            <MudCardContent>
                                <MudText Typo="Typo.subtitle1" Class="mb-2">当前配置预览</MudText>
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudTextField Label="环境名称" 
                                                    Value="@CurrentConfig.Environment" 
                                                    ReadOnly="true" 
                                                    Variant="Variant.Outlined" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudTextField Label="服务器地址" 
                                                    Value="@CurrentConfig.BaseUrl" 
                                                    ReadOnly="true" 
                                                    Variant="Variant.Outlined" />
                                    </MudItem>
                                    <MudItem xs="4">
                                        <MudCheckBox @bind-Checked="CurrentConfig.UseHttps" 
                                                   Label="使用HTTPS" 
                                                   ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="4">
                                        <MudCheckBox @bind-Checked="CurrentConfig.ValidateSslCertificate" 
                                                   Label="验证SSL证书" 
                                                   ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="4">
                                        <MudTextField Label="超时时间(秒)" 
                                                    Value="@CurrentConfig.RequestTimeoutSeconds.ToString()" 
                                                    ReadOnly="true" 
                                                    Variant="Variant.Outlined" />
                                    </MudItem>
                                </MudGrid>

                                @if (CurrentConfig.UseHttps && CurrentConfig.Ssl != null)
                                {
                                    <MudDivider Class="my-4" />
                                    <MudText Typo="Typo.subtitle2" Class="mb-2">SSL配置</MudText>
                                    <MudGrid>
                                        <MudItem xs="4">
                                            <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreNameMismatch" 
                                                       Label="忽略名称不匹配" 
                                                       ReadOnly="true" />
                                        </MudItem>
                                        <MudItem xs="4">
                                            <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreChainErrors" 
                                                       Label="忽略证书链错误" 
                                                       ReadOnly="true" />
                                        </MudItem>
                                        <MudItem xs="4">
                                            <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreAllErrors" 
                                                       Label="忽略所有SSL错误" 
                                                       ReadOnly="true" />
                                        </MudItem>
                                    </MudGrid>
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                }

                <MudItem xs="12">
                    <MudAlert Severity="Severity.Info" Class="mt-4">
                        <MudText Typo="Typo.body2">
                            <strong>环境说明：</strong><br/>
                            • <strong>Development</strong>: 开发环境，使用HTTP，忽略所有SSL验证<br/>
                            • <strong>IntranetHttp</strong>: 内网HTTP环境，适用于内网部署<br/>
                            • <strong>IntranetHttps</strong>: 内网HTTPS环境，允许IP访问时的证书名称不匹配<br/>
                            • <strong>Production</strong>: 生产环境，使用HTTPS，严格SSL验证
                        </MudText>
                    </MudAlert>
                </MudItem>
            </MudGrid>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveConfiguration">
            保存并应用
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    private string SelectedEnvironment = "Development";
    private NetworkConfiguration? CurrentConfig;
    private List<string> AvailableEnvironments = new();

    protected override void OnInitialized()
    {
        AvailableEnvironments = NetworkConfigurationManager.GetAvailableEnvironments();
        var currentConfig = NetworkConfigurationManager.GetCurrentConfiguration();
        SelectedEnvironment = currentConfig.Environment;
        UpdateCurrentConfig();
    }

    private void UpdateCurrentConfig()
    {
        if (NetworkConfigurationManager.PredefinedConfigurations.TryGetValue(SelectedEnvironment, out var config))
        {
            CurrentConfig = config;
            StateHasChanged();
        }
    }

    private string GetEnvironmentDisplayName(string environment)
    {
        return environment switch
        {
            "Development" => "开发环境 (HTTP)",
            "IntranetHttp" => "内网环境 (HTTP)",
            "IntranetHttps" => "内网环境 (HTTPS)",
            "Production" => "生产环境 (HTTPS)",
            _ => environment
        };
    }

    private async Task SaveConfiguration()
    {
        try
        {
            await NetworkConfigurationManager.SetEnvironmentAsync(SelectedEnvironment);
            Snackbar.Add($"网络环境已切换到: {GetEnvironmentDisplayName(SelectedEnvironment)}", Severity.Success);
            Snackbar.Add("请重启应用以使配置生效", Severity.Info);
            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存配置失败: {ex.Message}", Severity.Error);
        }
    }

    private void Cancel() => MudDialog.Cancel();

    protected override void OnParametersSet()
    {
        UpdateCurrentConfig();
    }
}
