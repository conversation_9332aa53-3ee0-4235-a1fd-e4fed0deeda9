namespace CoreHub.Web.Configuration
{
    /// <summary>
    /// HTTPS策略配置选项
    /// </summary>
    public class HttpsPolicyOptions
    {
        /// <summary>
        /// 配置节名称
        /// </summary>
        public const string SectionName = "HttpsPolicy";

        /// <summary>
        /// 是否启用HTTPS重定向
        /// </summary>
        public bool UseHttpsRedirection { get; set; } = false;

        /// <summary>
        /// 是否要求HTTPS
        /// </summary>
        public bool RequireHttps { get; set; } = false;

        /// <summary>
        /// 是否启用HSTS (HTTP Strict Transport Security)
        /// </summary>
        public bool UseHsts { get; set; } = false;

        /// <summary>
        /// HSTS最大年龄（天）
        /// </summary>
        public int HstsMaxAge { get; set; } = 30;

        /// <summary>
        /// HSTS是否包含子域名
        /// </summary>
        public bool IncludeSubdomains { get; set; } = false;

        /// <summary>
        /// 环境名称
        /// </summary>
        public string Environment { get; set; } = "Development";

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, List<string> Errors) Validate()
        {
            var errors = new List<string>();

            // 生产环境应该启用HTTPS
            if (Environment.Equals("Production", StringComparison.OrdinalIgnoreCase))
            {
                if (!UseHttpsRedirection)
                {
                    errors.Add("生产环境应该启用HTTPS重定向");
                }
                if (!RequireHttps)
                {
                    errors.Add("生产环境应该要求HTTPS");
                }
                if (!UseHsts)
                {
                    errors.Add("生产环境建议启用HSTS");
                }
            }

            // HSTS配置验证
            if (UseHsts)
            {
                if (HstsMaxAge <= 0)
                {
                    errors.Add("HSTS最大年龄必须大于0");
                }
                if (HstsMaxAge > 365 * 2) // 2年
                {
                    errors.Add("HSTS最大年龄不应超过2年");
                }
            }

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// 获取配置摘要
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetSummary()
        {
            var features = new List<string>();
            
            if (UseHttpsRedirection) features.Add("HTTPS重定向");
            if (RequireHttps) features.Add("要求HTTPS");
            if (UseHsts) features.Add($"HSTS({HstsMaxAge}天)");
            
            var featuresText = features.Any() ? string.Join(", ", features) : "无HTTPS强制";
            return $"{Environment}环境: {featuresText}";
        }

        /// <summary>
        /// 从环境变量覆盖配置
        /// </summary>
        public void OverrideFromEnvironment()
        {
            // 允许通过环境变量覆盖关键设置
            var forceHttpsEnv = System.Environment.GetEnvironmentVariable("FORCE_HTTPS");
            if (bool.TryParse(forceHttpsEnv, out var forceHttps))
            {
                UseHttpsRedirection = forceHttps;
                RequireHttps = forceHttps;
                UseHsts = forceHttps;
            }

            var hstsMaxAgeEnv = System.Environment.GetEnvironmentVariable("HSTS_MAX_AGE");
            if (int.TryParse(hstsMaxAgeEnv, out var hstsMaxAge))
            {
                HstsMaxAge = hstsMaxAge;
            }

            var environmentEnv = System.Environment.GetEnvironmentVariable("HTTPS_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentEnv))
            {
                Environment = environmentEnv;
            }
        }
    }

    /// <summary>
    /// HTTPS策略配置扩展方法
    /// </summary>
    public static class HttpsPolicyExtensions
    {
        /// <summary>
        /// 添加HTTPS策略配置
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddHttpsPolicy(this IServiceCollection services, IConfiguration configuration)
        {
            // 绑定配置
            var httpsPolicyOptions = new HttpsPolicyOptions();
            configuration.GetSection(HttpsPolicyOptions.SectionName).Bind(httpsPolicyOptions);
            
            // 从环境变量覆盖
            httpsPolicyOptions.OverrideFromEnvironment();
            
            // 验证配置
            var (isValid, errors) = httpsPolicyOptions.Validate();
            if (!isValid)
            {
                throw new InvalidOperationException($"HTTPS策略配置无效: {string.Join(", ", errors)}");
            }

            // 注册为单例服务
            services.AddSingleton(httpsPolicyOptions);
            
            return services;
        }

        /// <summary>
        /// 使用HTTPS策略
        /// </summary>
        /// <param name="app">应用构建器</param>
        /// <returns>应用构建器</returns>
        public static IApplicationBuilder UseHttpsPolicy(this IApplicationBuilder app)
        {
            var httpsPolicyOptions = app.ApplicationServices.GetRequiredService<HttpsPolicyOptions>();
            var logger = app.ApplicationServices.GetRequiredService<ILogger<HttpsPolicyOptions>>();

            logger.LogInformation("HTTPS策略配置: {Summary}", httpsPolicyOptions.GetSummary());

            // 根据配置启用HSTS
            if (httpsPolicyOptions.UseHsts)
            {
                logger.LogInformation("启用HSTS: 最大年龄={MaxAge}天, 包含子域名={IncludeSubdomains}", 
                    httpsPolicyOptions.HstsMaxAge, httpsPolicyOptions.IncludeSubdomains);
                
                app.UseHsts();
            }

            // 根据配置启用HTTPS重定向
            if (httpsPolicyOptions.UseHttpsRedirection)
            {
                logger.LogInformation("启用HTTPS重定向");
                app.UseHttpsRedirection();
            }
            else
            {
                logger.LogInformation("HTTPS重定向已禁用 - 允许HTTP和HTTPS并存");
            }

            return app;
        }
    }
}
