{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:8080"}, "Https": {"Url": "https://0.0.0.0:8081", "Certificate": {"Path": "Certificates/api_saintyeartex_com.pfx", "Password": "HO4LokI5cBvEuKYT"}, "Protocols": "Http1AndHttp2", "SslProtocols": ["Tls12", "Tls13"]}}}, "CertificateMonitoring": {"Enabled": false, "CheckIntervalHours": 24, "WarningDays": 30, "CriticalDays": 7}, "ConnectionStrings": {"DefaultConnection": "Server=**********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;", "LoggingConnection": "Server=**********;Database=CoreHub_Logs;User Id=sa;Password=****;TrustServerCertificate=True;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/corehub-web-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Security": {"RequireHttps": false, "UseHsts": false}, "Cors": {"AllowAnyOrigin": true, "AllowCredentials": true}, "HttpsPolicy": {"UseHttpsRedirection": false, "RequireHttps": false, "UseHsts": false, "HstsMaxAge": 30, "IncludeSubdomains": false, "Environment": "Intranet", "Description": "内网环境 - 允许HTTP和HTTPS并存，无强制重定向"}, "UpdateService": {"BaseUrl": "http://************:8080", "UpdatesDirectory": "wwwroot/updates", "MaxFileSize": 104857600, "AllowedFileTypes": [".apk", ".ipa", ".msix", ".dmg"]}}