@page "/network-settings"
@using CoreHub.Configuration
@using MudBlazor
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>网络设置</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">网络环境设置</MudText>

    <MudGrid>
        <MudItem xs="12" md="8">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">当前网络配置</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudIconButton Icon="Icons.Material.Filled.Settings" 
                                     Color="Color.Primary" 
                                     OnClick="OpenConfigurationDialog" />
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    @if (CurrentConfig != null)
                    {
                        <MudGrid>
                            <MudItem xs="12" sm="6">
                                <MudTextField Label="当前环境" 
                                            Value="@GetEnvironmentDisplayName(CurrentConfig.Environment)" 
                                            ReadOnly="true" 
                                            Variant="Variant.Outlined" />
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudTextField Label="服务器地址" 
                                            Value="@CurrentConfig.BaseUrl" 
                                            ReadOnly="true" 
                                            Variant="Variant.Outlined" />
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudCheckBox @bind-Checked="CurrentConfig.UseHttps" 
                                           Label="使用HTTPS" 
                                           ReadOnly="true" />
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudCheckBox @bind-Checked="CurrentConfig.ValidateSslCertificate" 
                                           Label="验证SSL证书" 
                                           ReadOnly="true" />
                            </MudItem>
                            <MudItem xs="12" sm="4">
                                <MudTextField Label="超时时间(秒)" 
                                            Value="@CurrentConfig.RequestTimeoutSeconds.ToString()" 
                                            ReadOnly="true" 
                                            Variant="Variant.Outlined" />
                            </MudItem>
                        </MudGrid>

                        @if (CurrentConfig.UseHttps && CurrentConfig.Ssl != null)
                        {
                            <MudDivider Class="my-4" />
                            <MudText Typo="Typo.subtitle1" Class="mb-2">SSL配置详情</MudText>
                            <MudGrid>
                                <MudItem xs="12" sm="4">
                                    <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreNameMismatch" 
                                               Label="忽略名称不匹配" 
                                               ReadOnly="true" />
                                </MudItem>
                                <MudItem xs="12" sm="4">
                                    <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreChainErrors" 
                                               Label="忽略证书链错误" 
                                               ReadOnly="true" />
                                </MudItem>
                                <MudItem xs="12" sm="4">
                                    <MudCheckBox @bind-Checked="CurrentConfig.Ssl.IgnoreAllErrors" 
                                               Label="忽略所有SSL错误" 
                                               ReadOnly="true" />
                                </MudItem>
                            </MudGrid>

                            @if (CurrentConfig.Ssl.TrustedHosts.Any())
                            {
                                <MudText Typo="Typo.subtitle2" Class="mt-3 mb-1">受信任的主机</MudText>
                                <MudChipSet>
                                    @foreach (var host in CurrentConfig.Ssl.TrustedHosts)
                                    {
                                        <MudChip Text="@host" Color="Color.Info" />
                                    }
                                </MudChipSet>
                            }
                        }
                    }
                </MudCardContent>
                <MudCardActions>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="Icons.Material.Filled.Edit"
                             OnClick="OpenConfigurationDialog">
                        切换环境
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             Color="Color.Secondary" 
                             StartIcon="Icons.Material.Filled.Refresh"
                             OnClick="RefreshConfiguration">
                        刷新配置
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">可用环境</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        @foreach (var env in AvailableEnvironments)
                        {
                            <MudListItem>
                                <div class="d-flex align-center">
                                    @if (CurrentConfig?.Environment == env)
                                    {
                                        <MudIcon Icon="Icons.Material.Filled.CheckCircle" Color="Color.Success" Class="mr-3" />
                                    }
                                    else
                                    {
                                        <MudIcon Icon="Icons.Material.Filled.RadioButtonUnchecked" Color="Color.Default" Class="mr-3" />
                                    }
                                    <div>
                                        <MudText Typo="Typo.body1">@GetEnvironmentDisplayName(env)</MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                                            @GetEnvironmentDescription(env)
                                        </MudText>
                                    </div>
                                </div>
                            </MudListItem>
                        }
                    </MudList>
                </MudCardContent>
            </MudCard>

            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">注意事项</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudAlert Severity="Severity.Warning">
                        <MudText Typo="Typo.body2">
                            • 切换环境后需要重启应用才能生效<br/>
                            • 生产环境使用严格的SSL验证<br/>
                            • 内网环境可能需要特殊的SSL配置
                        </MudText>
                    </MudAlert>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private NetworkConfiguration? CurrentConfig;
    private List<string> AvailableEnvironments = new();

    protected override void OnInitialized()
    {
        RefreshConfiguration();
    }

    private void RefreshConfiguration()
    {
        CurrentConfig = NetworkConfigurationManager.GetCurrentConfiguration();
        AvailableEnvironments = NetworkConfigurationManager.GetAvailableEnvironments();
        StateHasChanged();
    }

    private async Task OpenConfigurationDialog()
    {
        var dialog = await DialogService.ShowAsync<NetworkConfigurationDialog>("网络环境配置");
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            RefreshConfiguration();
        }
    }

    private string GetEnvironmentDisplayName(string environment)
    {
        return environment switch
        {
            "Development" => "开发环境",
            "IntranetHttp" => "内网HTTP",
            "IntranetHttps" => "内网HTTPS", 
            "Production" => "生产环境",
            _ => environment
        };
    }

    private string GetEnvironmentDescription(string environment)
    {
        return environment switch
        {
            "Development" => "HTTP协议，无SSL验证",
            "IntranetHttp" => "内网HTTP，适用于开发测试",
            "IntranetHttps" => "内网HTTPS，允许IP访问",
            "Production" => "公网HTTPS，严格SSL验证",
            _ => ""
        };
    }
}
