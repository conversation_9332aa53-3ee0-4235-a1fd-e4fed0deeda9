using Android.Content;
using Android.Content.PM;
using AndroidX.Core.Content;
using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;
using System.Security.Cryptography;
using System.Text.Json;
using Android.Net;
using Android.OS;
using AndroidX.Core.App;
using Android.App;
using AndroidSettings = Android.Provider.Settings;
using JavaLocale = Java.Util.Locale;
using JavaTimeZone = Java.Util.TimeZone;

namespace CoreHub.Platforms.Android
{
    /// <summary>
    /// Android平台更新服务实现，整合了SimpleUpdateService的成功逻辑
    /// </summary>
    public class AndroidUpdateService : IClientUpdateService
    {
        private readonly HttpClient _httpClient;
        private readonly IApplicationLogger _logger;
        private readonly string _baseUrl;
        private readonly string _downloadPath;
        private Timer? _autoCheckTimer;
        private int _checkIntervalHours = 24; // 默认24小时检查一次
        private UpdateCheckResponse? _latestUpdateInfo;
        private static bool _isUpdating = false;
        private static bool _hasChecked = false;

        public event EventHandler<UpdateCheckResponse>? UpdateAvailable;
        public event EventHandler<DownloadProgress>? DownloadProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;

        /// <summary>
        /// 显示更新页面
        /// </summary>
        public async Task ShowUpdatePageAsync()
        {
            try
            {
                _logger.LogInformation("开始显示更新页面");

                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    _logger.LogInformation("在主线程中创建更新页面");

                    var app = Microsoft.Maui.Controls.Application.Current;
                    if (app == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Application.Current 为 null");
                        return;
                    }

                    var mainPage = app.MainPage;
                    if (mainPage == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Application.Current.MainPage 为 null");
                        return;
                    }

                    var navigation = mainPage.Navigation;
                    if (navigation == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Navigation 为 null");
                        return;
                    }

                    _logger.LogInformation("创建UpdatePage实例，传递更新信息");
                    var updatePage = new UpdatePage(this, _logger, _latestUpdateInfo);

                    _logger.LogInformation("检查MainPage类型: {MainPageType}", mainPage.GetType().Name);

                    // 检查MainPage是否是Shell（AppShell）
                    if (mainPage is Shell shell)
                    {
                        _logger.LogInformation("MainPage是Shell，使用Shell导航");

                        // 创建导航参数
                        var navigationParameter = new Dictionary<string, object>();
                        if (_latestUpdateInfo != null)
                        {
                            navigationParameter["UpdateResponse"] = _latestUpdateInfo;
                        }
                        navigationParameter["UpdateService"] = this;
                        navigationParameter["Logger"] = _logger;

                        await shell.GoToAsync("UpdatePage", navigationParameter);
                        _logger.LogInformation("已导航到更新页面");
                    }
                    // 检查MainPage是否是NavigationPage
                    else if (mainPage is NavigationPage navigationPage)
                    {
                        _logger.LogInformation("MainPage是NavigationPage，使用PushAsync");
                        await navigationPage.PushAsync(updatePage);
                        _logger.LogInformation("更新页面已成功推送到NavigationPage");
                    }
                    else
                    {
                        _logger.LogInformation("MainPage不是Shell或NavigationPage，创建新的NavigationPage");
                        // 如果MainPage不是Shell或NavigationPage，我们需要创建一个新的NavigationPage
                        var newNavigationPage = new NavigationPage(updatePage);
                        app.MainPage = newNavigationPage;
                        _logger.LogInformation("已将更新页面设置为新的MainPage");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示更新页面时发生异常: {Message}", ex.Message);
            }
        }

        public AndroidUpdateService(HttpClient httpClient, IApplicationLogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = "https://************:8081"; // 硬编码基础URL

            // 设置HttpClient的BaseAddress
            if (_httpClient.BaseAddress == null)
            {
                _httpClient.BaseAddress = new System.Uri(_baseUrl);
            }

            // 设置下载目录
            var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
            _downloadPath = Path.Combine(context.GetExternalFilesDir(null)?.AbsolutePath ?? context.FilesDir.AbsolutePath, "updates");

            // 确保下载目录存在
            Directory.CreateDirectory(_downloadPath);
        }

        /// <summary>
        /// 是否正在更新
        /// </summary>
        public bool IsUpdating => _isUpdating;

        /// <summary>
        /// 是否已检查过更新
        /// </summary>
        public bool HasChecked => _hasChecked;

        /// <summary>
        /// 是否强制更新
        /// </summary>
        public bool IsForceUpdate => _latestUpdateInfo?.IsForceUpdate ?? false;

        /// <summary>
        /// 检查更新
        /// </summary>
        public async Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = false)
        {
            try
            {
                _isUpdating = true;
                var platform = Microsoft.Maui.Devices.DeviceInfo.Platform == DevicePlatform.Android ? "android" : "ios";

                _logger.LogInformation("正在检查更新，URL: {BaseUrl}/api/AppUpdate/check", _baseUrl);
                _logger.LogInformation("网络状态: {NetworkAccess}", Connectivity.NetworkAccess);

                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    _logger.LogWarning("无网络连接");
                    _isUpdating = false;
                    return new UpdateCheckResponse { HasUpdate = false, Message = "无网络连接" };
                }

                var (currentVersion, currentVersionCode) = await GetCurrentVersionAsync();
                var deviceInfo = await GetDeviceInfoAsync();

                var request = new UpdateCheckRequest
                {
                    CurrentVersion = currentVersion,
                    CurrentVersionCode = currentVersionCode,
                    Platform = "Android",
                    DeviceId = GetDeviceId(),
                    Device = deviceInfo
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var response = await _httpClient.PostAsync("/api/AppUpdate/check", content, cts.Token);

                _logger.LogInformation("服务器响应状态码: {StatusCode}", response.StatusCode);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("服务器返回错误: {StatusCode}", response.StatusCode);
                    _isUpdating = false;
                    return new UpdateCheckResponse { HasUpdate = false, Message = "检查更新失败" };
                }

                var jsonString = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("服务器响应内容: {JsonString}", jsonString);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                _latestUpdateInfo = JsonSerializer.Deserialize<UpdateCheckResponse>(jsonString, options);
                _logger.LogInformation("反序列化后的版本号: {Version}", _latestUpdateInfo?.LatestVersion?.VersionNumber ?? "null");

                var currentVersionString = AppInfo.Current.VersionString;
                _logger.LogInformation("当前版本: {CurrentVersion}, 最新版本: {LatestVersion}",
                    currentVersionString, _latestUpdateInfo?.LatestVersion?.VersionNumber ?? "null");

                if (_latestUpdateInfo?.HasUpdate != true)
                {
                    _logger.LogInformation("没有可用更新");
                    _isUpdating = false;
                }
                else
                {
                    // 触发更新可用事件
                    UpdateAvailable?.Invoke(this, _latestUpdateInfo);

                    // 如果是强制更新，直接显示更新页面
                    if (_latestUpdateInfo.IsForceUpdate)
                    {
                        _logger.LogInformation("检测到强制更新，显示更新页面");
                        await ShowUpdatePageAsync();
                    }
                    // 如果是静默检查且有更新，显示通知
                    else if (silent)
                    {
                        _logger.LogInformation("静默检查发现更新，显示通知");
                        await ShowUpdateNotificationAsync(_latestUpdateInfo);
                    }
                    else
                    {
                        _logger.LogInformation("非静默检查发现可选更新，不自动显示页面");
                    }
                }

                _hasChecked = true;
                return _latestUpdateInfo ?? new UpdateCheckResponse { HasUpdate = false, Message = "检查更新失败" };
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                _logger.LogError(ex, "检查更新时出错: {Message}", ex.Message);
                _hasChecked = true;
                return new UpdateCheckResponse { HasUpdate = false, Message = "检查更新失败" };
            }
        }

        /// <summary>
        /// 下载更新
        /// </summary>
        public async Task<(bool IsSuccess, string? FilePath, string? ErrorMessage)> DownloadUpdateAsync(
            VersionInfo versionInfo,
            IProgress<DownloadProgress>? progress = null,
            CancellationToken cancellationToken = default)
        {
            const int MaxRetries = 3;  // 最大重试次数
            const int BufferSize = 8192;  // 增大缓冲区大小
            int retryCount = 0;

            while (retryCount < MaxRetries)
            {
                try
                {
                    var fileName = $"CoreHub_{versionInfo.VersionNumber}.apk";
                    var tempPath = Path.Combine(_downloadPath, $"temp_{Guid.NewGuid()}.apk");
                    var filePath = Path.Combine(_downloadPath, fileName);

                    // 清理可能存在的临时文件
                    await CleanupTempFilesAsync();

                    // 确保目录存在
                    Directory.CreateDirectory(_downloadPath);

                    // 删除可能存在的旧文件
                    if (File.Exists(filePath)) File.Delete(filePath);

                    // 构建完整的下载URL - 使用相对URL
                    string downloadUrl;
                    if (System.Uri.IsWellFormedUriString(versionInfo.DownloadUrl, UriKind.Absolute))
                    {
                        downloadUrl = versionInfo.DownloadUrl;
                    }
                    else
                    {
                        downloadUrl = versionInfo.DownloadUrl.StartsWith("/") ? versionInfo.DownloadUrl : "/" + versionInfo.DownloadUrl;
                    }

                    _logger.LogInformation("开始下载更新，URL: {DownloadUrl}，第{RetryCount}次尝试", downloadUrl, retryCount + 1);

                    using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(10)); // 10分钟超时
                    using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, cts.Token);

                    using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead, combinedCts.Token);

                    _logger.LogInformation("下载响应状态码: {StatusCode}", response.StatusCode);

                    if (!response.IsSuccessStatusCode)
                    {
                        _logger.LogWarning("服务器返回错误: {StatusCode}", response.StatusCode);
                        retryCount++;
                        if (retryCount < MaxRetries)
                        {
                            await Task.Delay(1000 * retryCount, cancellationToken);
                        }
                        continue;
                    }

                    var totalBytes = response.Content.Headers.ContentLength ?? -1L;
                    var downloadedBytes = 0L;

                    using var downloadStream = await response.Content.ReadAsStreamAsync(combinedCts.Token);
                    using var fileStream = new FileStream(tempPath, FileMode.Create, FileAccess.Write, FileShare.None, BufferSize);

                    var buffer = new byte[BufferSize];
                    int bytesRead;
                    var lastProgressReport = DateTime.Now;

                    _logger.LogInformation("开始下载，总大小: {TotalBytes} bytes", totalBytes);

                    while ((bytesRead = await downloadStream.ReadAsync(buffer.AsMemory(0, buffer.Length), combinedCts.Token)) > 0)
                    {
                        await fileStream.WriteAsync(buffer.AsMemory(0, bytesRead), combinedCts.Token);
                        downloadedBytes += bytesRead;

                        // 报告进度 - 每秒最多报告一次
                        if (totalBytes > 0 && progress != null && (DateTime.Now - lastProgressReport).TotalMilliseconds > 500)
                        {
                            lastProgressReport = DateTime.Now;
                            var downloadProgress = new DownloadProgress
                            {
                                BytesReceived = downloadedBytes,
                                TotalBytesToReceive = totalBytes
                            };
                            progress.Report(downloadProgress);

                            // 触发下载进度事件
                            DownloadProgressChanged?.Invoke(this, downloadProgress);
                        }
                    }

                    // 确保写入所有数据
                    await fileStream.FlushAsync(combinedCts.Token);
                    fileStream.Close();

                    // 验证下载是否完整
                    var fileInfo = new FileInfo(tempPath);
                    _logger.LogInformation("下载完成，文件大小: {FileLength} bytes, 预期大小: {TotalBytes} bytes", fileInfo.Length, totalBytes);

                    if (totalBytes > 0 && fileInfo.Length != totalBytes)
                    {
                        _logger.LogWarning("下载不完整: {FileLength} vs {TotalBytes}", fileInfo.Length, totalBytes);
                        if (File.Exists(tempPath)) File.Delete(tempPath);
                        retryCount++;
                        if (retryCount < MaxRetries)
                        {
                            await Task.Delay(1000 * retryCount, cancellationToken);
                            continue;
                        }
                        throw new Exception($"下载不完整: 已下载 {fileInfo.Length} bytes, 需要 {totalBytes} bytes");
                    }

                    // 移动文件到最终位置
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                            _logger.LogInformation("删除已存在的目标文件");
                        }

                        File.Move(tempPath, filePath);
                        _logger.LogInformation("文件移动成功到: {FilePath}", filePath);

                        // 验证最终文件
                        if (!File.Exists(filePath))
                        {
                            _logger.LogError(new Exception("文件移动后不存在"), "最终文件不存在");
                            throw new Exception("文件移动后不存在");
                        }

                        var finalFileInfo = new FileInfo(filePath);
                        _logger.LogInformation("最终文件大小: {FileLength} bytes", finalFileInfo.Length);

                        // 触发下载完成事件
                        DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
                        {
                            IsSuccess = true,
                            FilePath = filePath,
                            VersionInfo = versionInfo
                        });

                        return (true, filePath, null);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "文件处理过程出错: {Message}", ex.Message);
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "下载出错 (尝试 {RetryCount}/{MaxRetries}): {Message}", retryCount + 1, MaxRetries, ex.Message);
                    retryCount++;
                    if (retryCount < MaxRetries)
                    {
                        await Task.Delay(1000 * retryCount, cancellationToken);  // 递增延迟
                    }
                }
            }

            _logger.LogError(new Exception("下载失败"), "下载失败，已重试 {MaxRetries} 次", MaxRetries);

            // 触发下载完成事件（失败）
            DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
            {
                IsSuccess = false,
                ErrorMessage = $"下载失败，已重试 {MaxRetries} 次",
                VersionInfo = versionInfo
            });

            return (false, null, $"下载失败，已重试 {MaxRetries} 次");
        }

        /// <summary>
        /// 安装更新
        /// </summary>
        public async Task<(bool IsSuccess, string? ErrorMessage)> InstallUpdateAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("准备安装APK: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogError(new Exception("APK文件不存在"), "APK文件不存在: {FilePath}", filePath);
                    return (false, "APK文件不存在");
                }

                var fileInfo = new FileInfo(filePath);
                _logger.LogInformation("APK文件大小: {FileLength} bytes", fileInfo.Length);

                var context = global::Android.App.Application.Context;
                var intent = new Intent(Intent.ActionView);

                var fileUri = AndroidX.Core.Content.FileProvider.GetUriForFile(
                    context,
                    $"{AppInfo.Current.PackageName}.fileprovider",
                    new Java.IO.File(filePath));

                _logger.LogInformation("文件URI: {FileUri}", fileUri);

                intent.SetDataAndType(fileUri, "application/vnd.android.package-archive");
                intent.AddFlags(ActivityFlags.GrantReadUriPermission | ActivityFlags.NewTask | ActivityFlags.ClearTop);

                _logger.LogInformation("启动安装活动");
                context.StartActivity(intent);
                _logger.LogInformation("安装活动已启动");

                // 等待一段时间确保安装对话框显示
                await Task.Delay(1000);

                return (true, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安装更新失败: {Message}", ex.Message);
                return (false, ex.Message);
            }
        }

        /// <summary>
        /// 验证更新文件
        /// </summary>
        public async Task<bool> ValidateUpdateFileAsync(string filePath, string expectedMd5)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                using var md5 = MD5.Create();
                using var stream = File.OpenRead(filePath);
                var hash = await Task.Run(() => md5.ComputeHash(stream));
                var actualMd5 = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();

                return string.Equals(actualMd5, expectedMd5, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证文件时发生异常: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 获取当前应用版本信息
        /// </summary>
        public async Task<(string VersionNumber, int VersionCode)> GetCurrentVersionAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                    var packageManager = context.PackageManager;
                    var packageInfo = packageManager?.GetPackageInfo(context.PackageName!, 0);

                    if (packageInfo != null)
                    {
                        var versionName = packageInfo.VersionName ?? "1.0.0";
                        var versionCode = Build.VERSION.SdkInt >= BuildVersionCodes.P ? 
                            (int)packageInfo.LongVersionCode : packageInfo.VersionCode;

                        return (versionName, versionCode);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取应用版本信息时发生异常");
                }

                return ("1.0.0", 1);
            });
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        public async Task<CoreHub.Shared.Models.AppUpdate.DeviceInfo> GetDeviceInfoAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo
                    {
                        Model = $"{Build.Manufacturer} {Build.Model}",
                        OsVersion = Build.VERSION.Release,
                        Language = JavaLocale.Default?.Language ?? "zh",
                        TimeZone = JavaTimeZone.Default?.ID ?? "Asia/Shanghai"
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取设备信息时发生异常");
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo();
                }
            });
        }

        /// <summary>
        /// 清理旧的更新文件
        /// </summary>
        public async Task<bool> CleanupOldUpdatesAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (!Directory.Exists(_downloadPath))
                    {
                        return true;
                    }

                    var files = Directory.GetFiles(_downloadPath, "*.apk");
                    var cutoffTime = DateTime.Now.AddDays(-7); // 删除7天前的文件

                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffTime)
                        {
                            File.Delete(file);
                            _logger.LogInformation("清理旧更新文件: {FilePath}", file);
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理旧更新文件时发生异常");
                    return false;
                }
            });
        }

        /// <summary>
        /// 设置更新检查间隔
        /// </summary>
        public void SetUpdateCheckInterval(int intervalHours)
        {
            _checkIntervalHours = Math.Max(1, intervalHours); // 最少1小时
            
            // 如果定时器正在运行，重新启动
            if (_autoCheckTimer != null)
            {
                StopAutoUpdateCheck();
                StartAutoUpdateCheck();
            }
        }

        /// <summary>
        /// 启动自动更新检查
        /// </summary>
        public void StartAutoUpdateCheck()
        {
            StopAutoUpdateCheck();

            var interval = TimeSpan.FromHours(_checkIntervalHours);
            _autoCheckTimer = new Timer(async _ =>
            {
                try
                {
                    await CheckForUpdateAsync(silent: true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "自动检查更新时发生异常");
                }
            }, null, TimeSpan.Zero, interval);

            _logger.LogInformation("启动自动更新检查，间隔: {IntervalHours} 小时", _checkIntervalHours);
        }

        /// <summary>
        /// 停止自动更新检查
        /// </summary>
        public void StopAutoUpdateCheck()
        {
            _autoCheckTimer?.Dispose();
            _autoCheckTimer = null;
            _logger.LogInformation("停止自动更新检查");
        }

        /// <summary>
        /// 获取设备ID
        /// </summary>
        private string GetDeviceId()
        {
            try
            {
                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                return AndroidSettings.Secure.GetString(context.ContentResolver, AndroidSettings.Secure.AndroidId) ?? "unknown";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备ID时发生异常");
                return "unknown";
            }
        }



        /// <summary>
        /// 显示更新通知
        /// </summary>
        private async Task ShowUpdateNotificationAsync(UpdateCheckResponse updateResponse)
        {
            try
            {
                if (updateResponse.LatestVersion == null) return;

                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;

                // 创建通知渠道
                var channelId = "app_update_channel";
                var channelName = "应用更新";

                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    var notificationManagerCompat = NotificationManagerCompat.From(context);
                    var channel = new NotificationChannel(channelId, channelName, NotificationImportance.High)
                    {
                        Description = "应用更新通知"
                    };
                    notificationManagerCompat.CreateNotificationChannel(channel);
                }

                // 创建点击通知的Intent
                var intent = new Intent(context, typeof(MainActivity));
                intent.SetFlags(ActivityFlags.SingleTop | ActivityFlags.ClearTop);
                intent.PutExtra("show_update", true);

                var pendingIntent = PendingIntent.GetActivity(context, 0, intent,
                    PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);

                // 构建通知
                var notification = new NotificationCompat.Builder(context, channelId)
                    .SetContentTitle("发现新版本")
                    .SetContentText($"版本 {updateResponse.LatestVersion.VersionNumber} 可用，点击更新")
                    .SetSmallIcon(global::Android.Resource.Drawable.StatSysDownload)
                    .SetAutoCancel(true)
                    .SetContentIntent(pendingIntent)
                    .SetPriority(NotificationCompat.PriorityHigh)
                    .Build();

                // 显示通知
                var notificationManager2 = NotificationManagerCompat.From(context);
                if (notificationManager2.AreNotificationsEnabled())
                {
                    notificationManager2.Notify(1001, notification);
                    _logger.LogInformation("已显示更新通知: 版本 {Version}", updateResponse.LatestVersion.VersionNumber);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示更新通知时发生异常");
            }
        }

        /// <summary>
        /// 清理临时文件
        /// </summary>
        private async Task CleanupTempFilesAsync()
        {
            try
            {
                if (!Directory.Exists(_downloadPath))
                    return;

                var tempFiles = Directory.GetFiles(_downloadPath, "CoreHub_*.apk")
                    .Where(f => File.GetCreationTime(f) < DateTime.Now.AddDays(-1)) // 删除1天前的文件
                    .ToList();

                foreach (var tempFile in tempFiles)
                {
                    try
                    {
                        await DeleteFileWithRetryAsync(tempFile, 1); // 只重试1次
                        _logger.LogInformation("清理临时文件: {TempFile}", tempFile);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("清理临时文件失败: {TempFile}, 错误: {Error}", tempFile, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("清理临时文件时发生异常: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// 带重试的文件删除
        /// </summary>
        private async Task DeleteFileWithRetryAsync(string filePath, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        // 强制垃圾回收，释放可能的文件句柄
                        GC.Collect();
                        GC.WaitForPendingFinalizers();

                        File.Delete(filePath);
                        _logger.LogInformation("成功删除文件: {FilePath}", filePath);
                        return;
                    }
                    return; // 文件不存在，直接返回
                }
                catch (IOException ex) when (i < maxRetries - 1)
                {
                    _logger.LogWarning("删除文件失败，第 {Attempt} 次尝试: {Error}", i + 1, ex.Message);
                    await Task.Delay(1000 * (i + 1)); // 递增延迟：1s, 2s, 3s
                }
                catch (UnauthorizedAccessException ex) when (i < maxRetries - 1)
                {
                    _logger.LogWarning("删除文件权限不足，第 {Attempt} 次尝试: {Error}", i + 1, ex.Message);
                    await Task.Delay(1000 * (i + 1));
                }
            }

            // 所有重试都失败了
            throw new IOException($"无法删除文件 {filePath}，已重试 {maxRetries} 次");
        }

        public void Dispose()
        {
            StopAutoUpdateCheck();
            _httpClient?.Dispose();
        }
    }
}
