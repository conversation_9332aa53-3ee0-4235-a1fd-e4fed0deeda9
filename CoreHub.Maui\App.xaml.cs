using CoreHub.Shared.Services;

namespace CoreHub
{
    public partial class App : Application
    {
        private IClientUpdateService? _updateService;
        private IApplicationLogger? _logger;
        private bool _updateCheckInProgress = false;

        public App()
        {
            try
            {
                InitializeComponent();

                // 设置全局异常处理
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                MainPage = new AppShell();

                // 在构造函数中添加调试信息，这个一定会在应用启动时执行
                System.Diagnostics.Debug.WriteLine($"=== App 构造函数被调用 ===");
                System.Diagnostics.Debug.WriteLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 延迟显示构造函数调试对话框
                _ = Task.Run(async () =>
                {
                    await Task.Delay(3000); // 等待3秒确保UI完全加载
                    await MainThread.InvokeOnMainThreadAsync(async () =>
                    {
                        try
                        {
                            if (MainPage != null)
                            {
                                await MainPage.DisplayAlert("构造函数调试",
                                    $"App构造函数被调用\n时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n这证明应用确实重启了",
                                    "确定");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示构造函数调试对话框失败: {ex.Message}");
                        }
                    });
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"App Constructor Error: {ex}");
                throw;
            }
        }

        protected override async void OnStart()
        {
            base.OnStart();

            System.Diagnostics.Debug.WriteLine($"=== App.OnStart 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            System.Diagnostics.Debug.WriteLine($"_updateCheckInProgress: {_updateCheckInProgress}");

            // 显示调试对话框
            await Task.Delay(1000); // 等待UI初始化
            _ = Task.Run(async () =>
            {
                await Task.Delay(2000); // 再等待2秒确保UI完全加载
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        await MainPage.DisplayAlert("调试信息",
                            $"App.OnStart 被调用\n时间: {DateTime.Now:HH:mm:ss}\n_updateCheckInProgress: {_updateCheckInProgress}",
                            "确定");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"显示调试对话框失败: {ex.Message}");
                    }
                });

                // 启动更新检查
                await CheckForUpdatesAsync();
            });
        }

        protected override void OnSleep()
        {
            base.OnSleep();
            System.Diagnostics.Debug.WriteLine($"=== App.OnSleep 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        protected override void OnResume()
        {
            base.OnResume();
            System.Diagnostics.Debug.WriteLine($"=== App.OnResume 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            System.Diagnostics.Debug.WriteLine($"_updateCheckInProgress: {_updateCheckInProgress}");

            // 显示调试对话框
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // 等待1秒
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        await MainPage.DisplayAlert("调试信息",
                            $"App.OnResume 被调用\n时间: {DateTime.Now:HH:mm:ss}\n_updateCheckInProgress: {_updateCheckInProgress}",
                            "确定");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"显示OnResume调试对话框失败: {ex.Message}");
                    }
                });

                // 应用从后台恢复时也检查更新
                await CheckForUpdatesAsync();
            });
        }

        private async Task CheckForUpdatesAsync()
        {
            System.Diagnostics.Debug.WriteLine($"=== CheckForUpdatesAsync 开始 ===");
            System.Diagnostics.Debug.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            System.Diagnostics.Debug.WriteLine($"_updateCheckInProgress: {_updateCheckInProgress}");

            // 防止重复检查
            if (_updateCheckInProgress)
            {
                System.Diagnostics.Debug.WriteLine("更新检查已在进行中，跳过重复检查");
                return;
            }

            try
            {
                _updateCheckInProgress = true;
                System.Diagnostics.Debug.WriteLine("设置 _updateCheckInProgress = true");
                // 尝试从服务提供器获取服务
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider != null)
                {
                    _updateService = serviceProvider.GetService<IClientUpdateService>();
                    _logger = serviceProvider.GetService<IApplicationLogger>();
                }

                if (_updateService == null || _logger == null)
                {
                    System.Diagnostics.Debug.WriteLine("更新服务未初始化");

                    // 显示调试对话框
                    await MainThread.InvokeOnMainThreadAsync(async () =>
                    {
                        try
                        {
                            await MainPage.DisplayAlert("调试信息",
                                $"更新服务未初始化\n_updateService: {(_updateService != null ? "已初始化" : "null")}\n_logger: {(_logger != null ? "已初始化" : "null")}",
                                "确定");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示服务初始化调试对话框失败: {ex.Message}");
                        }
                    });
                    return;
                }

                // 检查网络连接
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    _logger.LogWarning("无网络连接，跳过更新检查");

                    // 显示调试对话框
                    await MainThread.InvokeOnMainThreadAsync(async () =>
                    {
                        try
                        {
                            await MainPage.DisplayAlert("调试信息",
                                $"无网络连接\n网络状态: {Connectivity.NetworkAccess}",
                                "确定");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示网络调试对话框失败: {ex.Message}");
                        }
                    });
                    return;
                }

                _logger.LogInformation("开始检查更新...");

                // 重置更新检查状态并强制刷新版本信息（确保每次启动都能正常检查）
#if ANDROID
                if (_updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                {
                    androidUpdateService.ResetUpdateCheckStatus();

                    // 强制刷新当前版本信息
                    var (currentVersion, currentVersionCode) = await androidUpdateService.RefreshCurrentVersionAsync();
                    _logger.LogInformation("应用启动时版本信息 - Version: {Version}, VersionCode: {VersionCode}", currentVersion, currentVersionCode);
                }
#endif

                // 检查更新
                _logger.LogInformation("开始调用 CheckForUpdateAsync...");
                var updateResponse = await _updateService.CheckForUpdateAsync(silent: true);
                _logger.LogInformation("CheckForUpdateAsync 调用完成，HasUpdate: {HasUpdate}", updateResponse?.HasUpdate);

                // 显示更新检查结果的调试对话框
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        await MainPage.DisplayAlert("调试信息",
                            $"更新检查完成\nHasUpdate: {updateResponse?.HasUpdate}\nIsForceUpdate: {updateResponse?.IsForceUpdate}\n版本: {updateResponse?.LatestVersion?.VersionNumber ?? "null"}",
                            "确定");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"显示更新检查结果调试对话框失败: {ex.Message}");
                    }
                });

                if (updateResponse.HasUpdate)
                {
                    if (updateResponse.IsForceUpdate)
                    {
                        _logger.LogInformation("检测到强制更新，更新页面将自动显示");
                        // 强制更新会在CheckForUpdateAsync中自动显示更新页面
                    }
                    else
                    {
                        _logger.LogInformation("检测到可选更新，显示通知和确认对话框");
                        // AndroidUpdateService已经显示了通知
                        // 现在显示确认对话框
                        bool shouldUpdate = await MainPage.DisplayAlert(
                            "发现新版本",
                            $"发现新版本 {updateResponse.LatestVersion?.VersionNumber}\n\n是否立即更新？",
                            "立即更新",
                            "稍后提醒");

                        if (shouldUpdate)
                        {
                            await ShowUpdatePageAndStartDownloadAsync();
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("当前已是最新版本");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "检查更新失败: {Message}", ex.Message);
                System.Diagnostics.Debug.WriteLine($"检查更新失败: {ex.Message}");
            }
            finally
            {
                _updateCheckInProgress = false;
                System.Diagnostics.Debug.WriteLine("设置 _updateCheckInProgress = false");
                System.Diagnostics.Debug.WriteLine($"=== CheckForUpdatesAsync 结束 ===");
            }
        }

        private async Task ShowUpdatePageAsync()
        {
            try
            {
                if (_updateService == null || _logger == null)
                {
                    return;
                }

                // 如果是AndroidUpdateService，直接调用其ShowUpdatePageAsync方法
#if ANDROID
                if (_updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                {
                    await androidUpdateService.ShowUpdatePageAsync();
                }
                else
#endif
                {
                    _logger.LogWarning("当前平台不支持显示更新页面");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "显示更新页面失败: {Message}", ex.Message);
                System.Diagnostics.Debug.WriteLine($"显示更新页面失败: {ex.Message}");
            }
        }

        private async Task ShowUpdatePageAndStartDownloadAsync()
        {
            try
            {
                if (_updateService == null || _logger == null)
                {
                    return;
                }

                // 如果是AndroidUpdateService，调用显示更新页面并开始下载的方法
#if ANDROID
                if (_updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                {
                    await androidUpdateService.ShowUpdatePageAndStartDownloadAsync();
                }
                else
#endif
                {
                    _logger.LogWarning("当前平台不支持显示更新页面");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "显示更新页面并开始下载失败: {Message}", ex.Message);
                System.Diagnostics.Debug.WriteLine($"显示更新页面并开始下载失败: {ex.Message}");
            }
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"Unhandled Exception: {exception}");

            // 在这里可以添加错误报告或日志记录
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"Unobserved Task Exception: {e.Exception}");
            e.SetObserved(); // 防止应用崩溃
        }
    }
}
