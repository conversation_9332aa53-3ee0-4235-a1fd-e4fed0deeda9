# CoreHub Web 环境快速切换脚本
# 使用方法: .\switch-environment.ps1 -Environment Production

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Intranet", "Staging", "Production")]
    [string]$Environment,
    
    [switch]$ShowConfig,
    [switch]$TestConnection
)

Write-Host "=== CoreHub Web 环境切换 ===" -ForegroundColor Green
Write-Host "目标环境: $Environment" -ForegroundColor Yellow

# 环境配置映射
$EnvironmentConfigs = @{
    "Development" = @{
        "ASPNETCORE_ENVIRONMENT" = "Development"
        "DEPLOYMENT_ENVIRONMENT" = "Development"
        "FORCE_HTTPS" = "false"
        "ALLOWED_HOSTS" = "*"
        "CORS_ORIGINS" = ""
        "Description" = "开发环境 - HTTP协议，无SSL验证"
        "BaseUrl" = "http://localhost:8080"
        "HttpsUrl" = "https://localhost:8081"
    }
    "Intranet" = @{
        "ASPNETCORE_ENVIRONMENT" = "Intranet"
        "DEPLOYMENT_ENVIRONMENT" = "Intranet"
        "FORCE_HTTPS" = "false"
        "ALLOWED_HOSTS" = "*"
        "CORS_ORIGINS" = ""
        "Description" = "内网环境 - HTTP/HTTPS并存，宽松SSL验证"
        "BaseUrl" = "http://************:8080"
        "HttpsUrl" = "https://************:8081"
    }
    "Staging" = @{
        "ASPNETCORE_ENVIRONMENT" = "Staging"
        "DEPLOYMENT_ENVIRONMENT" = "Staging"
        "FORCE_HTTPS" = "true"
        "ALLOWED_HOSTS" = "staging.saintyeartex.com,*.staging.saintyeartex.com"
        "CORS_ORIGINS" = "https://staging.saintyeartex.com,https://app.staging.saintyeartex.com"
        "Description" = "预发布环境 - 强制HTTPS，中等SSL验证"
        "BaseUrl" = "https://staging.saintyeartex.com:8081"
        "HttpsUrl" = "https://staging.saintyeartex.com:8081"
    }
    "Production" = @{
        "ASPNETCORE_ENVIRONMENT" = "Production"
        "DEPLOYMENT_ENVIRONMENT" = "Production"
        "FORCE_HTTPS" = "true"
        "ALLOWED_HOSTS" = "api.saintyeartex.com,*.saintyeartex.com"
        "CORS_ORIGINS" = "https://api.saintyeartex.com,https://app.saintyeartex.com,https://admin.saintyeartex.com"
        "Description" = "生产环境 - 强制HTTPS，严格SSL验证"
        "BaseUrl" = "https://api.saintyeartex.com:8081"
        "HttpsUrl" = "https://api.saintyeartex.com:8081"
    }
}

$config = $EnvironmentConfigs[$Environment]

if (-not $config) {
    Write-Host "❌ 未知的环境: $Environment" -ForegroundColor Red
    exit 1
}

Write-Host "环境描述: $($config.Description)" -ForegroundColor Cyan

# 设置环境变量
Write-Host "设置环境变量..." -ForegroundColor Yellow
foreach ($key in $config.Keys) {
    if ($key -notin @("Description", "BaseUrl", "HttpsUrl")) {
        [Environment]::SetEnvironmentVariable($key, $config[$key], "Process")
        Write-Host "  $key = $($config[$key])" -ForegroundColor Gray
    }
}

# 显示配置信息
if ($ShowConfig) {
    Write-Host ""
    Write-Host "=== 当前环境配置 ===" -ForegroundColor Cyan
    Write-Host "环境名称: $Environment" -ForegroundColor White
    Write-Host "描述: $($config.Description)" -ForegroundColor White
    Write-Host "HTTP地址: $($config.BaseUrl)" -ForegroundColor White
    Write-Host "HTTPS地址: $($config.HttpsUrl)" -ForegroundColor White
    Write-Host "强制HTTPS: $($config.FORCE_HTTPS)" -ForegroundColor White
    Write-Host "允许的主机: $($config.ALLOWED_HOSTS)" -ForegroundColor White
    Write-Host "CORS源: $($config.CORS_ORIGINS)" -ForegroundColor White
}

# 测试连接
if ($TestConnection) {
    Write-Host ""
    Write-Host "=== 测试连接 ===" -ForegroundColor Cyan
    
    # 测试HTTP连接
    try {
        Write-Host "测试HTTP连接: $($config.BaseUrl)" -ForegroundColor Yellow
        $httpResponse = Invoke-WebRequest -Uri "$($config.BaseUrl)/health" -Method GET -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✅ HTTP连接成功 (状态码: $($httpResponse.StatusCode))" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ HTTP连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试HTTPS连接（如果启用）
    if ($config.FORCE_HTTPS -eq "true") {
        try {
            Write-Host "测试HTTPS连接: $($config.HttpsUrl)" -ForegroundColor Yellow
            $httpsResponse = Invoke-WebRequest -Uri "$($config.HttpsUrl)/health" -Method GET -TimeoutSec 10 -ErrorAction Stop
            Write-Host "✅ HTTPS连接成功 (状态码: $($httpsResponse.StatusCode))" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ HTTPS连接失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "=== 环境切换完成 ===" -ForegroundColor Green
Write-Host "当前环境: $Environment" -ForegroundColor Yellow

# 显示下一步操作
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
switch ($Environment) {
    "Development" {
        Write-Host "1. 运行: dotnet run --project CoreHub.Web" -ForegroundColor White
        Write-Host "2. 访问: http://localhost:8080" -ForegroundColor White
    }
    "Intranet" {
        Write-Host "1. 运行: .\Scripts\deploy-intranet.ps1" -ForegroundColor White
        Write-Host "2. 访问: http://************:8080" -ForegroundColor White
    }
    "Production" {
        Write-Host "1. 运行: .\Scripts\deploy-production.ps1" -ForegroundColor White
        Write-Host "2. 访问: https://api.saintyeartex.com:8081" -ForegroundColor White
        Write-Host "3. 确保SSL证书有效" -ForegroundColor White
    }
}

# 显示MAUI客户端配置建议
Write-Host ""
Write-Host "MAUI客户端配置建议:" -ForegroundColor Cyan
switch ($Environment) {
    "Development" {
        Write-Host "BaseUrl: http://localhost:8080" -ForegroundColor White
        Write-Host "SSL验证: 禁用" -ForegroundColor White
    }
    "Intranet" {
        Write-Host "BaseUrl: http://************:8080" -ForegroundColor White
        Write-Host "SSL验证: 禁用" -ForegroundColor White
    }
    "Production" {
        Write-Host "BaseUrl: https://api.saintyeartex.com:8081" -ForegroundColor White
        Write-Host "SSL验证: 启用（严格）" -ForegroundColor White
    }
}
