-- 添加网络设置菜单项脚本
-- 执行时间：2025-01-18

USE [CoreHub]
GO

PRINT '开始添加网络设置菜单项...';

-- 查找系统管理分组ID
DECLARE @SystemManagementId INT;
SELECT @SystemManagementId = Id FROM MenuItems WHERE Code = 'SystemManagement';

IF @SystemManagementId IS NOT NULL
BEGIN
    -- 检查网络设置菜单是否已存在
    IF NOT EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'NetworkSettings')
    BEGIN
        -- 添加网络设置菜单项
        INSERT INTO [dbo].[MenuItems] (
            [Code], 
            [Name], 
            [Description], 
            [RouteUrl], 
            [Icon], 
            [ParentId], 
            [Level], 
            [SortOrder], 
            [PermissionCode], 
            [IsEnabled], 
            [IsPublic], 
            [IsSystem], 
            [MenuType]
        )
        VALUES (
            'NetworkSettings',                          -- Code
            '网络设置',                                 -- Name
            '网络环境配置和SSL设置管理',                -- Description
            'network-settings',                         -- RouteUrl
            'Icons.Material.Filled.NetworkCheck',       -- Icon
            @SystemManagementId,                        -- ParentId
            2,                                          -- Level
            106,                                        -- SortOrder (在其他系统管理菜单之后)
            'SystemSettings.NetworkConfig',             -- PermissionCode
            1,                                          -- IsEnabled
            0,                                          -- IsPublic
            1,                                          -- IsSystem
            1                                           -- MenuType (菜单项)
        );
        
        PRINT '✅ 已成功添加网络设置菜单项';
        
        -- 显示添加的菜单信息
        SELECT 
            Code,
            Name,
            Description,
            RouteUrl,
            Icon,
            SortOrder,
            PermissionCode
        FROM MenuItems 
        WHERE Code = 'NetworkSettings';
        
    END
    ELSE
    BEGIN
        PRINT '⚠️ 网络设置菜单项已存在，跳过添加';
    END
END
ELSE
BEGIN
    PRINT '❌ 错误：找不到系统管理菜单分组，请先运行基础菜单初始化脚本';
END

-- 验证菜单层级结构
PRINT '';
PRINT '当前系统管理菜单结构：';
SELECT 
    CASE WHEN Level = 1 THEN Name ELSE '  └─ ' + Name END AS MenuStructure,
    Code,
    RouteUrl,
    SortOrder,
    CASE WHEN IsEnabled = 1 THEN '启用' ELSE '禁用' END AS Status
FROM MenuItems 
WHERE (Code = 'SystemManagement' OR ParentId = @SystemManagementId)
ORDER BY Level, SortOrder;

PRINT '';
PRINT '网络设置菜单添加完成！';
