using CoreHub.Shared.Services;

namespace CoreHub.Platforms.Android
{
    /// <summary>
    /// Android平台的更新UI服务实现
    /// </summary>
    public class AndroidUpdateUIService : IUpdateUIService
    {
        private readonly IClientUpdateService _updateService;
        private readonly IApplicationLogger _logger;

        public AndroidUpdateUIService(IClientUpdateService updateService, IApplicationLogger logger)
        {
            _updateService = updateService;
            _logger = logger;
        }

        /// <summary>
        /// 手动检查更新并显示更新页面
        /// </summary>
        public async Task CheckForUpdateAsync()
        {
            try
            {
                _logger.LogInformation("开始手动检查更新");
                
                // 检查更新
                var updateResponse = await _updateService.CheckForUpdateAsync(silent: false);
                
                if (updateResponse?.HasUpdate == true)
                {
                    if (updateResponse.IsForceUpdate)
                    {
                        // 强制更新直接显示更新页面
                        await ShowUpdatePageAsync();
                    }
                    else
                    {
                        // 可选更新显示确认对话框
                        if (_updateService is AndroidUpdateService androidUpdateService)
                        {
                            await androidUpdateService.ShowUpdateConfirmationDialogAsync();
                        }
                        else
                        {
                            await ShowUpdatePageAsync();
                        }
                    }
                }
                else
                {
                    // 没有更新，显示提示
                    await MainThread.InvokeOnMainThreadAsync(async () =>
                    {
                        await Application.Current?.MainPage?.DisplayAlert("检查更新", "当前已是最新版本", "确定")!;
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动检查更新时发生异常");
                
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    await Application.Current?.MainPage?.DisplayAlert("错误", $"检查更新失败：{ex.Message}", "确定")!;
                });
            }
        }

        /// <summary>
        /// 显示更新页面
        /// </summary>
        public async Task ShowUpdatePageAsync()
        {
            try
            {
                if (_updateService is AndroidUpdateService androidUpdateService)
                {
                    await androidUpdateService.ShowUpdatePageAsync();
                }
                else
                {
                    _logger.LogWarning("更新服务不是Android实现，无法显示更新页面");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示更新页面时发生异常");
                
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    await Application.Current?.MainPage?.DisplayAlert("错误", $"显示更新页面失败：{ex.Message}", "确定")!;
                });
            }
        }
    }
}
