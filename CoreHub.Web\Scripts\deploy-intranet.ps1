# CoreHub Web 内网环境部署脚本
# 使用方法: .\deploy-intranet.ps1

param(
    [string]$Environment = "Intranet",
    [string]$PublishPath = ".\publish\intranet",
    [switch]$ForceHttps = $false,
    [string]$ServerIP = "************"
)

Write-Host "=== CoreHub Web 内网环境部署 ===" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "发布路径: $PublishPath" -ForegroundColor Yellow
Write-Host "强制HTTPS: $ForceHttps" -ForegroundColor Yellow
Write-Host "服务器IP: $ServerIP" -ForegroundColor Yellow

# 设置环境变量
$env:ASPNETCORE_ENVIRONMENT = $Environment
$env:DEPLOYMENT_ENVIRONMENT = "Intranet"
$env:FORCE_HTTPS = $ForceHttps.ToString().ToLower()
$env:ALLOWED_HOSTS = "*"
$env:CORS_ORIGINS = ""

Write-Host "设置的环境变量:" -ForegroundColor Cyan
Write-Host "  ASPNETCORE_ENVIRONMENT = $env:ASPNETCORE_ENVIRONMENT"
Write-Host "  DEPLOYMENT_ENVIRONMENT = $env:DEPLOYMENT_ENVIRONMENT"
Write-Host "  FORCE_HTTPS = $env:FORCE_HTTPS"
Write-Host "  ALLOWED_HOSTS = $env:ALLOWED_HOSTS"

# 清理之前的发布
if (Test-Path $PublishPath) {
    Write-Host "清理之前的发布文件..." -ForegroundColor Yellow
    Remove-Item $PublishPath -Recurse -Force
}

# 发布应用
Write-Host "开始发布应用..." -ForegroundColor Yellow
dotnet publish CoreHub.Web.csproj -c Release -o $PublishPath --self-contained false

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 应用发布成功!" -ForegroundColor Green
    
    # 创建启动脚本
    $startScript = @"
#!/bin/bash
# CoreHub Web 内网环境启动脚本

export ASPNETCORE_ENVIRONMENT=$Environment
export DEPLOYMENT_ENVIRONMENT=Intranet
export FORCE_HTTPS=$($ForceHttps.ToString().ToLower())
export ALLOWED_HOSTS="*"
export CORS_ORIGINS=""

echo "=== CoreHub Web 内网启动 ==="
echo "环境: `$ASPNETCORE_ENVIRONMENT"
echo "部署环境: `$DEPLOYMENT_ENVIRONMENT"
echo "强制HTTPS: `$FORCE_HTTPS"
echo "服务器IP: $ServerIP"

# 启动应用
dotnet CoreHub.Web.dll --urls "http://0.0.0.0:8080;https://0.0.0.0:8081"
"@

    $startScript | Out-File -FilePath "$PublishPath\start-intranet.sh" -Encoding UTF8
    
    # 创建Windows启动脚本
    $startBat = @"
@echo off
REM CoreHub Web 内网环境启动脚本

set ASPNETCORE_ENVIRONMENT=$Environment
set DEPLOYMENT_ENVIRONMENT=Intranet
set FORCE_HTTPS=$($ForceHttps.ToString().ToLower())
set ALLOWED_HOSTS=*
set CORS_ORIGINS=

echo === CoreHub Web 内网启动 ===
echo 环境: %ASPNETCORE_ENVIRONMENT%
echo 部署环境: %DEPLOYMENT_ENVIRONMENT%
echo 强制HTTPS: %FORCE_HTTPS%
echo 服务器IP: $ServerIP

REM 启动应用
dotnet CoreHub.Web.dll --urls "http://0.0.0.0:8080;https://0.0.0.0:8081"
"@

    $startBat | Out-File -FilePath "$PublishPath\start-intranet.bat" -Encoding UTF8
    
    Write-Host "✅ 启动脚本已创建:" -ForegroundColor Green
    Write-Host "  Linux/Mac: start-intranet.sh" -ForegroundColor Cyan
    Write-Host "  Windows: start-intranet.bat" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "=== 部署完成 ===" -ForegroundColor Green
    Write-Host "发布文件位置: $PublishPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "内网访问地址:" -ForegroundColor Cyan
    Write-Host "  HTTP: http://${ServerIP}:8080" -ForegroundColor White
    Write-Host "  HTTPS: https://${ServerIP}:8081 (如果启用)" -ForegroundColor White
    Write-Host ""
    Write-Host "MAUI客户端配置:" -ForegroundColor Cyan
    Write-Host "  BaseUrl: http://${ServerIP}:8080" -ForegroundColor White
    Write-Host "  无需SSL配置" -ForegroundColor White
    
} else {
    Write-Host "❌ 应用发布失败!" -ForegroundColor Red
    exit 1
}
