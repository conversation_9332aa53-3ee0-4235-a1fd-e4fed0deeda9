﻿using Microsoft.Extensions.Logging;
using CoreHub.Shared.Services;
using CoreHub.Services;
using ZXing.Net.Maui;
using ZXing.Net.Maui.Controls;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using CoreHub.Shared.Configuration;
using MudBlazor.Services;
using Microsoft.Extensions.Http;

#if ANDROID
using CoreHub.Platforms.Android;
#endif

namespace CoreHub
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseBarcodeReader()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddMudServices();

            // 注册HttpClient服务
            builder.Services.AddHttpClient();

            // 配置HttpClient处理器
            builder.Services.ConfigureAll<HttpClientFactoryOptions>(options =>
            {
                options.HttpMessageHandlerBuilderActions.Add(builder =>
                {
                    // 为内网环境配置SSL证书验证
                    builder.PrimaryHandler = new HttpClientHandler()
                    {
                        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
                        {
                            // 记录SSL验证详细信息用于调试
                            System.Diagnostics.Debug.WriteLine("=== SSL证书验证开始 ===");
                            System.Diagnostics.Debug.WriteLine($"请求URL: {message.RequestUri}");
                            System.Diagnostics.Debug.WriteLine($"证书主题: {cert?.Subject}");
                            System.Diagnostics.Debug.WriteLine($"证书颁发者: {cert?.Issuer}");
                            System.Diagnostics.Debug.WriteLine($"证书有效期: {cert?.NotBefore} - {cert?.NotAfter}");
                            System.Diagnostics.Debug.WriteLine($"SSL错误: {errors}");

                            // 如果没有SSL错误，允许连接
                            if (errors == System.Net.Security.SslPolicyErrors.None)
                            {
                                System.Diagnostics.Debug.WriteLine("SSL验证成功：无错误");
                                return true;
                            }

                            // 检查具体的SSL错误类型
                            if (errors.HasFlag(System.Net.Security.SslPolicyErrors.RemoteCertificateNameMismatch))
                            {
                                System.Diagnostics.Debug.WriteLine("SSL错误：证书名称不匹配");
                                // 检查是否是通过IP访问导致的名称不匹配
                                if (message.RequestUri?.Host == "************")
                                {
                                    System.Diagnostics.Debug.WriteLine("通过IP访问，允许名称不匹配（用于调试）");
                                    return true; // 临时允许IP访问
                                }
                                return false;
                            }

                            if (errors.HasFlag(System.Net.Security.SslPolicyErrors.RemoteCertificateChainErrors))
                            {
                                System.Diagnostics.Debug.WriteLine("SSL错误：证书链错误");
                                if (chain != null)
                                {
                                    foreach (var status in chain.ChainStatus)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"证书链状态: {status.Status} - {status.StatusInformation}");
                                    }
                                }
                                return false;
                            }

                            if (errors.HasFlag(System.Net.Security.SslPolicyErrors.RemoteCertificateNotAvailable))
                            {
                                System.Diagnostics.Debug.WriteLine("SSL错误：远程证书不可用");
                                return false;
                            }

                            System.Diagnostics.Debug.WriteLine("SSL验证失败：未知错误");
                            return false;
                        }
                    };
                });
            });

            // 使用统一的配置管理
            builder.Configuration.AddInMemoryCollection(DatabaseConfig.GetConfigurationData());

            // 添加Authorization服务
            builder.Services.AddAuthorizationCore();

            // 注册FormFactor服务
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            // 注册应用程序日志服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IApplicationLogger, MauiApplicationLogger>();

            // 根据不同平台注册不同的服务实现
#if ANDROID
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.Android.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Android.QrCodeScannerService>();
            // 注册Android平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Android.AndroidAuthenticationStateStorage>();
            // 注册Android平台的更新服务
            builder.Services.AddSingleton<IClientUpdateService, Platforms.Android.AndroidUpdateService>();
            // 注册更新页面
            builder.Services.AddTransient<Platforms.Android.UpdatePage>();
            // 注册更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, Platforms.Android.AndroidUpdateUIService>();
#elif IOS
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.iOS.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.iOS.QrCodeScannerService>();
            // 注册iOS平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.iOS.iOSAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#elif MACCATALYST
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.MacCatalyst.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.MacCatalyst.QrCodeScannerService>();
            // 注册MacCatalyst平台的认证状态存储（创建MacCatalyst版本）
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.MacCatalyst.MacCatalystAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#elif WINDOWS
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 注册Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#else
            builder.Services.AddSingleton<INotificationService, Platforms.Windows.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 默认使用Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#endif

            // 注册数据库上下文
            builder.Services.AddScoped<CoreHub.Shared.Data.DatabaseContext>();

            // 根据配置选择认证服务实现（与Web项目完全一致）
            var useStoredProcedure = builder.Configuration.GetValue<bool>("AuthenticationSettings:UseStoredProcedure");
            if (useStoredProcedure)
            {
                builder.Services.AddScoped<IUserAuthenticationService, CoreHub.Shared.Services.StoredProcedureAuthenticationService>();
            }
            else
            {
                builder.Services.AddScoped<IUserAuthenticationService, CoreHub.Shared.Services.DatabaseAuthenticationService>();
            }

            // 注册用户管理服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IUserManagementService, CoreHub.Shared.Services.UserManagementService>();

            // 注册菜单服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IMenuService, CoreHub.Shared.Services.MenuService>();

            // 注册设备管理服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IDepartmentService, CoreHub.Shared.Services.DepartmentService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IEquipmentModelService, CoreHub.Shared.Services.EquipmentModelService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.ILocationService, CoreHub.Shared.Services.LocationService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IEquipmentService, CoreHub.Shared.Services.EquipmentService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairOrderPartRequestService, CoreHub.Shared.Services.RepairOrderPartRequestService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairOrderService, CoreHub.Shared.Services.RepairOrderService>();

            // 注册权限服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IRoleDepartmentPermissionService, CoreHub.Shared.Services.RoleDepartmentPermissionService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRoleDepartmentAssignmentServiceV2, CoreHub.Shared.Services.RoleDepartmentAssignmentServiceV2>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IMaintenanceDepartmentPermissionService, CoreHub.Shared.Services.MaintenanceDepartmentPermissionService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IPermissionValidationService, CoreHub.Shared.Services.PermissionValidationService>();

            // 注册部门类型和工种类型服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IDepartmentTypeService, CoreHub.Shared.Services.DepartmentTypeService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IJobTypeService, CoreHub.Shared.Services.JobTypeService>();

            // 注册维修工作流服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IMaintenanceDashboardService, CoreHub.Shared.Services.MaintenanceDashboardService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairWorkflowService, CoreHub.Shared.Services.RepairWorkflowService>();

            // 注册应用更新服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IAppUpdateService, CoreHub.Shared.Services.AppUpdateService>();

            // 注册持久化认证状态提供器
            builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

#if DEBUG
    		builder.Services.AddBlazorWebViewDeveloperTools();
    		builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
