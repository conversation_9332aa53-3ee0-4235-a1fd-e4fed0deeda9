using Microsoft.Extensions.Logging;
using CoreHub.Shared.Services;
using CoreHub.Services;
using ZXing.Net.Maui;
using ZXing.Net.Maui.Controls;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using CoreHub.Shared.Configuration;
using MudBlazor.Services;
using Microsoft.Extensions.Http;

#if ANDROID
using CoreHub.Platforms.Android;
#endif

namespace CoreHub
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseBarcodeReader()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddMudServices();

            // 注册HttpClient服务
            builder.Services.AddHttpClient();

            // 配置HttpClient处理器
            builder.Services.ConfigureAll<HttpClientFactoryOptions>(options =>
            {
                options.HttpMessageHandlerBuilderActions.Add(builder =>
                {
                    // 为内网环境配置SSL证书验证
                    builder.PrimaryHandler = new HttpClientHandler()
                    {
                        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
                        {
                            // 在生产环境中，应该验证特定的证书指纹或主机名
                            // 这里为内网环境提供更安全的验证方式

                            if (cert == null) return false;

                            // 验证服务器主机名
                            var expectedHost = "************";
                            if (message.RequestUri?.Host != expectedHost)
                            {
                                System.Diagnostics.Debug.WriteLine($"SSL验证失败: 主机名不匹配。期望: {expectedHost}, 实际: {message.RequestUri?.Host}");
                                return false;
                            }

                            // 验证证书主题名称（可选）
                            var subjectName = cert.Subject;
                            System.Diagnostics.Debug.WriteLine($"SSL证书主题: {subjectName}");

                            // 对于内网自签名证书，我们信任特定主机的证书
                            // 在生产环境中，建议验证证书指纹
                            return true;
                        }
                    };
                });
            });

            // 使用统一的配置管理
            builder.Configuration.AddInMemoryCollection(DatabaseConfig.GetConfigurationData());

            // 添加Authorization服务
            builder.Services.AddAuthorizationCore();

            // 注册FormFactor服务
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            // 注册应用程序日志服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IApplicationLogger, MauiApplicationLogger>();

            // 根据不同平台注册不同的服务实现
#if ANDROID
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.Android.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Android.QrCodeScannerService>();
            // 注册Android平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Android.AndroidAuthenticationStateStorage>();
            // 注册Android平台的更新服务
            builder.Services.AddSingleton<IClientUpdateService, Platforms.Android.AndroidUpdateService>();
            // 注册更新页面
            builder.Services.AddTransient<Platforms.Android.UpdatePage>();
            // 注册更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, Platforms.Android.AndroidUpdateUIService>();
#elif IOS
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.iOS.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.iOS.QrCodeScannerService>();
            // 注册iOS平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.iOS.iOSAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#elif MACCATALYST
            builder.Services.AddSingleton<CoreHub.Shared.Services.INotificationService, Platforms.MacCatalyst.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.MacCatalyst.QrCodeScannerService>();
            // 注册MacCatalyst平台的认证状态存储（创建MacCatalyst版本）
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.MacCatalyst.MacCatalystAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#elif WINDOWS
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 注册Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#else
            builder.Services.AddSingleton<INotificationService, Platforms.Windows.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 默认使用Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Services.DefaultUpdateUIService>();
#endif

            // 注册数据库上下文
            builder.Services.AddScoped<CoreHub.Shared.Data.DatabaseContext>();

            // 根据配置选择认证服务实现（与Web项目完全一致）
            var useStoredProcedure = builder.Configuration.GetValue<bool>("AuthenticationSettings:UseStoredProcedure");
            if (useStoredProcedure)
            {
                builder.Services.AddScoped<IUserAuthenticationService, CoreHub.Shared.Services.StoredProcedureAuthenticationService>();
            }
            else
            {
                builder.Services.AddScoped<IUserAuthenticationService, CoreHub.Shared.Services.DatabaseAuthenticationService>();
            }

            // 注册用户管理服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IUserManagementService, CoreHub.Shared.Services.UserManagementService>();

            // 注册菜单服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IMenuService, CoreHub.Shared.Services.MenuService>();

            // 注册设备管理服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IDepartmentService, CoreHub.Shared.Services.DepartmentService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IEquipmentModelService, CoreHub.Shared.Services.EquipmentModelService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.ILocationService, CoreHub.Shared.Services.LocationService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IEquipmentService, CoreHub.Shared.Services.EquipmentService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairOrderPartRequestService, CoreHub.Shared.Services.RepairOrderPartRequestService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairOrderService, CoreHub.Shared.Services.RepairOrderService>();

            // 注册权限服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IRoleDepartmentPermissionService, CoreHub.Shared.Services.RoleDepartmentPermissionService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRoleDepartmentAssignmentServiceV2, CoreHub.Shared.Services.RoleDepartmentAssignmentServiceV2>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IMaintenanceDepartmentPermissionService, CoreHub.Shared.Services.MaintenanceDepartmentPermissionService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IPermissionValidationService, CoreHub.Shared.Services.PermissionValidationService>();

            // 注册部门类型和工种类型服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IDepartmentTypeService, CoreHub.Shared.Services.DepartmentTypeService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IJobTypeService, CoreHub.Shared.Services.JobTypeService>();

            // 注册维修工作流服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IMaintenanceDashboardService, CoreHub.Shared.Services.MaintenanceDashboardService>();
            builder.Services.AddScoped<CoreHub.Shared.Services.IRepairWorkflowService, CoreHub.Shared.Services.RepairWorkflowService>();

            // 注册应用更新服务
            builder.Services.AddScoped<CoreHub.Shared.Services.IAppUpdateService, CoreHub.Shared.Services.AppUpdateService>();

            // 注册持久化认证状态提供器
            builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

#if DEBUG
    		builder.Services.AddBlazorWebViewDeveloperTools();
    		builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
