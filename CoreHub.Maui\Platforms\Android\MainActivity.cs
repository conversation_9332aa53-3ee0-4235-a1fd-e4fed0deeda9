﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Content;
using CoreHub.Shared.Services;

namespace CoreHub
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== MainActivity.OnCreate 被调用 ===");
                System.Diagnostics.Debug.WriteLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                System.Diagnostics.Debug.WriteLine($"savedInstanceState: {(savedInstanceState != null ? "有状态" : "无状态")}");

                base.OnCreate(savedInstanceState);

                // 显示MainActivity调试对话框
                _ = Task.Run(async () =>
                {
                    await Task.Delay(4000); // 等待4秒确保UI完全加载
                    try
                    {
                        var app = Microsoft.Maui.Controls.Application.Current;
                        if (app?.MainPage != null)
                        {
                            await app.MainPage.DisplayAlert("MainActivity调试",
                                $"MainActivity.OnCreate被调用\n时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\nsavedInstanceState: {(savedInstanceState != null ? "有状态(应用恢复)" : "无状态(全新启动)")}",
                                "确定");
                        }
                    }
                    catch (System.Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"显示MainActivity调试对话框失败: {ex.Message}");
                    }
                });

                // 检查是否是从更新通知点击进入的
                CheckUpdateNotificationIntent();
            }
            catch (System.Exception ex)
            {
                // 记录启动错误
                System.Diagnostics.Debug.WriteLine($"MainActivity OnCreate Error: {ex}");

                // 可以在这里添加更详细的错误处理
                // 比如显示错误对话框或重启应用
                throw;
            }
        }

        protected override void OnNewIntent(Intent? intent)
        {
            System.Diagnostics.Debug.WriteLine($"=== MainActivity.OnNewIntent 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            base.OnNewIntent(intent);
            Intent = intent;
            CheckUpdateNotificationIntent();
        }

        protected override void OnResume()
        {
            System.Diagnostics.Debug.WriteLine($"=== MainActivity.OnResume 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            base.OnResume();

            // 显示OnResume调试对话框
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000);
                try
                {
                    var app = Microsoft.Maui.Controls.Application.Current;
                    if (app?.MainPage != null)
                    {
                        await app.MainPage.DisplayAlert("MainActivity调试",
                            $"MainActivity.OnResume被调用\n时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                            "确定");
                    }
                }
                catch (System.Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"显示MainActivity OnResume调试对话框失败: {ex.Message}");
                }
            });
        }

        protected override void OnPause()
        {
            System.Diagnostics.Debug.WriteLine($"=== MainActivity.OnPause 被调用 ===");
            System.Diagnostics.Debug.WriteLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            base.OnPause();
        }

        private void CheckUpdateNotificationIntent()
        {
            try
            {
                if (Intent?.GetBooleanExtra("show_update", false) == true)
                {
                    // 延迟一下，等待应用完全加载
                    Task.Delay(2000).ContinueWith(async _ =>
                    {
                        try
                        {
                            // 获取更新服务并直接显示更新页面
                            var updateService = MauiApplication.Current?.Services?.GetService<IClientUpdateService>();
                            if (updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                            {
                                await androidUpdateService.ShowUpdatePageAsync();
                            }
                        }
                        catch (System.Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示更新页面时发生错误: {ex}");
                        }
                    });
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理更新通知Intent时发生错误: {ex}");
            }
        }


    }
}
