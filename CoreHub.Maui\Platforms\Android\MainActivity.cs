﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Content;
using CoreHub.Shared.Services;

namespace CoreHub
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            try
            {
                base.OnCreate(savedInstanceState);

                // 检查是否是从更新通知点击进入的
                CheckUpdateNotificationIntent();
            }
            catch (System.Exception ex)
            {
                // 记录启动错误
                System.Diagnostics.Debug.WriteLine($"MainActivity OnCreate Error: {ex}");

                // 可以在这里添加更详细的错误处理
                // 比如显示错误对话框或重启应用
                throw;
            }
        }

        protected override void OnNewIntent(Intent? intent)
        {
            base.OnNewIntent(intent);
            Intent = intent;
            CheckUpdateNotificationIntent();
        }

        private void CheckUpdateNotificationIntent()
        {
            try
            {
                if (Intent?.GetBooleanExtra("show_update", false) == true)
                {
                    // 延迟一下，等待应用完全加载
                    Task.Delay(2000).ContinueWith(async _ =>
                    {
                        try
                        {
                            // 获取更新服务并显示确认对话框
                            var updateService = MauiApplication.Current?.Services?.GetService<IClientUpdateService>();
                            if (updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                            {
                                await ShowUpdateConfirmationDialogAsync(androidUpdateService);
                            }
                        }
                        catch (System.Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示更新页面时发生错误: {ex}");
                        }
                    });
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理更新通知Intent时发生错误: {ex}");
            }
        }

        private async Task ShowUpdateConfirmationDialogAsync(Platforms.Android.AndroidUpdateService updateService)
        {
            try
            {
                // 使用AndroidUpdateService的确认对话框方法
                await updateService.ShowUpdateConfirmationDialogAsync();
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示更新确认对话框时发生错误: {ex}");
            }
        }
    }
}
