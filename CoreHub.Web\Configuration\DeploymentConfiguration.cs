namespace CoreHub.Web.Configuration
{
    /// <summary>
    /// 部署环境配置
    /// </summary>
    public class DeploymentConfiguration
    {
        public string Environment { get; set; } = "Development";
        public bool ForceHttps { get; set; } = false;
        public bool UseHttpsRedirection { get; set; } = false;
        public bool RequireHttpsForApi { get; set; } = false;
        public string[] AllowedHosts { get; set; } = { "*" };
        public CorsConfiguration Cors { get; set; } = new();
        public SslConfiguration Ssl { get; set; } = new();
    }

    public class CorsConfiguration
    {
        public bool AllowAnyOrigin { get; set; } = true;
        public string[] AllowedOrigins { get; set; } = Array.Empty<string>();
        public bool AllowCredentials { get; set; } = true;
    }

    public class SslConfiguration
    {
        public bool RequireHttps { get; set; } = false;
        public bool UseHsts { get; set; } = false;
        public int HstsMaxAge { get; set; } = 30; // days
        public bool IncludeSubdomains { get; set; } = false;
    }

    /// <summary>
    /// 部署配置管理器
    /// </summary>
    public static class DeploymentConfigurationManager
    {
        /// <summary>
        /// 预定义的部署环境配置
        /// </summary>
        public static readonly Dictionary<string, DeploymentConfiguration> Configurations = new()
        {
            ["Development"] = new DeploymentConfiguration
            {
                Environment = "Development",
                ForceHttps = false,
                UseHttpsRedirection = false,
                RequireHttpsForApi = false,
                AllowedHosts = new[] { "*" },
                Cors = new CorsConfiguration
                {
                    AllowAnyOrigin = true,
                    AllowCredentials = true
                },
                Ssl = new SslConfiguration
                {
                    RequireHttps = false,
                    UseHsts = false
                }
            },

            ["Intranet"] = new DeploymentConfiguration
            {
                Environment = "Intranet",
                ForceHttps = false,
                UseHttpsRedirection = false,
                RequireHttpsForApi = false,
                AllowedHosts = new[] { "*" },
                Cors = new CorsConfiguration
                {
                    AllowAnyOrigin = true,
                    AllowCredentials = true
                },
                Ssl = new SslConfiguration
                {
                    RequireHttps = false,
                    UseHsts = false
                }
            },

            ["Production"] = new DeploymentConfiguration
            {
                Environment = "Production",
                ForceHttps = true,
                UseHttpsRedirection = true,
                RequireHttpsForApi = true,
                AllowedHosts = new[] { "api.saintyeartex.com", "*.saintyeartex.com" },
                Cors = new CorsConfiguration
                {
                    AllowAnyOrigin = false,
                    AllowedOrigins = new[] 
                    { 
                        "https://api.saintyeartex.com",
                        "https://app.saintyeartex.com",
                        "https://admin.saintyeartex.com"
                    },
                    AllowCredentials = true
                },
                Ssl = new SslConfiguration
                {
                    RequireHttps = true,
                    UseHsts = true,
                    HstsMaxAge = 365,
                    IncludeSubdomains = true
                }
            },

            ["Staging"] = new DeploymentConfiguration
            {
                Environment = "Staging",
                ForceHttps = true,
                UseHttpsRedirection = true,
                RequireHttpsForApi = false,
                AllowedHosts = new[] { "staging.saintyeartex.com", "*.staging.saintyeartex.com" },
                Cors = new CorsConfiguration
                {
                    AllowAnyOrigin = false,
                    AllowedOrigins = new[] 
                    { 
                        "https://staging.saintyeartex.com",
                        "https://app.staging.saintyeartex.com"
                    },
                    AllowCredentials = true
                },
                Ssl = new SslConfiguration
                {
                    RequireHttps = true,
                    UseHsts = true,
                    HstsMaxAge = 30,
                    IncludeSubdomains = false
                }
            }
        };

        /// <summary>
        /// 获取当前部署配置
        /// </summary>
        /// <param name="environment">环境名称</param>
        /// <returns>部署配置</returns>
        public static DeploymentConfiguration GetConfiguration(string? environment = null)
        {
            environment ??= Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
            
            // 检查自定义部署环境变量
            var deploymentEnv = Environment.GetEnvironmentVariable("DEPLOYMENT_ENVIRONMENT");
            if (!string.IsNullOrEmpty(deploymentEnv))
            {
                environment = deploymentEnv;
            }

            if (Configurations.TryGetValue(environment, out var config))
            {
                return config;
            }

            // 默认返回开发环境配置
            return Configurations["Development"];
        }

        /// <summary>
        /// 从环境变量覆盖配置
        /// </summary>
        /// <param name="config">基础配置</param>
        /// <returns>覆盖后的配置</returns>
        public static DeploymentConfiguration OverrideFromEnvironment(DeploymentConfiguration config)
        {
            // 允许通过环境变量覆盖关键配置
            if (bool.TryParse(Environment.GetEnvironmentVariable("FORCE_HTTPS"), out var forceHttps))
            {
                config.ForceHttps = forceHttps;
                config.UseHttpsRedirection = forceHttps;
                config.RequireHttpsForApi = forceHttps;
                config.Ssl.RequireHttps = forceHttps;
            }

            var allowedHosts = Environment.GetEnvironmentVariable("ALLOWED_HOSTS");
            if (!string.IsNullOrEmpty(allowedHosts))
            {
                config.AllowedHosts = allowedHosts.Split(',', StringSplitOptions.RemoveEmptyEntries);
            }

            var corsOrigins = Environment.GetEnvironmentVariable("CORS_ORIGINS");
            if (!string.IsNullOrEmpty(corsOrigins))
            {
                config.Cors.AllowAnyOrigin = false;
                config.Cors.AllowedOrigins = corsOrigins.Split(',', StringSplitOptions.RemoveEmptyEntries);
            }

            return config;
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <param name="config">配置</param>
        /// <returns>验证结果</returns>
        public static (bool IsValid, List<string> Errors) ValidateConfiguration(DeploymentConfiguration config)
        {
            var errors = new List<string>();

            // 生产环境必须启用HTTPS
            if (config.Environment == "Production" && !config.ForceHttps)
            {
                errors.Add("生产环境必须启用HTTPS");
            }

            // 检查CORS配置
            if (!config.Cors.AllowAnyOrigin && !config.Cors.AllowedOrigins.Any())
            {
                errors.Add("CORS配置无效：未指定允许的源");
            }

            // 检查AllowedHosts
            if (config.Environment == "Production" && config.AllowedHosts.Contains("*"))
            {
                errors.Add("生产环境不应该允许所有主机");
            }

            return (errors.Count == 0, errors);
        }
    }
}
